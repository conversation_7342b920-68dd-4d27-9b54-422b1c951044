# modules/cloud-run/variables.tf
variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "region" {
  description = "GCP Region"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "service_name" {
  description = "Cloud Run service name"
  type        = string
}

variable "initial_image" {
  description = "Initial container image (will be updated by GitHub Actions)"
  type        = string
  default     = "gcr.io/cloudrun/hello"
}

variable "port" {
  description = "Container port"
  type        = number
  default     = 8080
}

variable "cpu" {
  description = "CPU limit"
  type        = string
  default     = "1"
}

variable "memory" {
  description = "Memory limit"
  type        = string
  default     = "512Mi"
}

variable "cpu_idle" {
  description = "CPU idle setting"
  type        = bool
  default     = true
}

variable "startup_cpu_boost" {
  description = "Enable startup CPU boost"
  type        = bool
  default     = false
}

variable "min_instances" {
  description = "Minimum instances"
  type        = number
  default     = 0
}

variable "max_instances" {
  description = "Maximum instances"
  type        = number
  default     = 100
}

variable "max_instance_request_concurrency" {
  description = "Maximum concurrent requests per instance"
  type        = number
  default     = 80
}

variable "timeout" {
  description = "Request timeout"
  type        = string
  default     = "300s"
}

variable "execution_environment" {
  description = "Execution environment (gen1 or gen2)"
  type        = string
  default     = "gen2"
}

variable "vpc_connector_name" {
  description = "VPC connector name (full resource name)"
  type        = string
  default     = ""
}

variable "vpc_egress" {
  description = "VPC egress setting"
  type        = string
  default     = "PRIVATE_RANGES_ONLY"
}

variable "allow_unauthenticated" {
  description = "Allow unauthenticated access"
  type        = bool
  default     = true
}

variable "env_vars" {
  description = "Environment variables"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "secret_env_vars" {
  description = "Environment variables from Secret Manager"
  type = list(object({
    name        = string
    secret_name = string
    version     = optional(string, null)
  }))
  default = []
}

variable "service_account_roles" {
  description = "IAM roles to grant to the Cloud Run service account"
  type        = list(string)
  default = [
    "roles/cloudsql.client",
    "roles/secretmanager.secretAccessor",
    "roles/storage.objectViewer",
  ]
}

variable "health_check_path" {
  description = "Path for health checks (null to disable)"
  type        = string
  default     = "/health"
}

variable "session_affinity" {
  description = "Enable session affinity"
  type        = bool
  default     = false
}

variable "annotations" {
  description = "Additional annotations for the service"
  type        = map(string)
  default     = {}
}

variable "labels" {
  description = "Additional labels for the service"
  type        = map(string)
  default     = {}
}

variable "cloudsql_connections" {
  description = "List of CloudSQL connection names to connect to"
  type        = list(string)
  default     = []
}
